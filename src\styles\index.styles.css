@media (min-width: 768px) {
  .typography-hero {
    font-size: 48px;
  }
}

.footer_parent_box {
  float: left;
  width: 100%;
  margin-top: auto;
}
.copyright_footer {
  float: left;
  width: 100%;
  display: inline-block;
}
.custom_footer {
  float: left;
  width: 100%;
  display: inline-block;
}

.home_banner > img {
  float: left;
  object-fit: cover;
  width: 100%;
  height: calc(100vh - 145px) !important;
}
.banner_mobile_img {
  display: none;
}

.home_banner_text h2 {
  font-family: Anton;
  font-size: 48px;
  font-weight: 400 !important;
  line-height: 48px;
  text-align: left;
  color: #000;
  padding-bottom: 16px;
  margin: 0px !important;
  text-transform: uppercase;
  letter-spacing: 0.12em;
}
.home_banner_fields input,
.home_banner_fields select {
  border: unset;
  background: #fff;
  height: 60px;
  padding: 0px 10px 0px 40px;
  color: rgba(0, 0, 0, 0.6);
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 18px;
  width: 100%;
  font-family: Poppins;
  border: unset;
  border-radius: 0px;
}
.banner_form_field {
  display: grid;
  width: 100%;
  grid-template-columns: auto;
  grid-template-rows: auto;
}
.banner_form_submit_btn {
  border-radius: unset;
  width: 100%;
  font-size: 24px;
  font-style: normal;
  font-weight: 500;
  line-height: normal;

  height: 60px;
}
.banner_submit_btn {
  display: grid;
  grid-template-columns: 1.5fr 1fr;
  gap: 10px 10px;
}
.dropdown_toggle > div > div > button {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  border: unset;

  border-radius: unset;
  padding: 0px 20px 0px 13px;
  color: rgba(0, 0, 0, 0.6);
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 18px;
  width: 100%;
  font-family: Poppins;
}
.dropdown_toggle > div > div > button svg {
  color: rgba(0, 0, 0, 1);
}

@media (max-width: 1024px) {
  .home_banner_text h2 {
    font-size: 38px;
  }
  .banner_form_box {
    width: 420px;
    padding: 30px 15px 0px 15px;
    margin: 0px;
  }
}
@media (max-width: 991px) {
  .home_banner > img {
    height: calc(100vh - 97px) !important;
  }
}
@media (max-width: 768px) {
  .banner_form_box {
    width: 100%;
    padding-left: 10px;
    padding-right: 10px;
  }
  .home_banner_text {
    width: 100%;
    max-width: 100%;
  }
}
@media (max-width: 540px) {
  .home_banner_text h2 {
    font-size: 28px;
  }
  .banner_mobile_img {
    display: block !important;
  }
  .banner_desk_img {
    display: none !important;
  }
  .home_banner_fields input,
  .home_banner_fields select {
    height: 50px;
    font-size: 14px;
  }
  .banner_form_submit_btn {
    font-size: 20px;
    height: 50px;
  }
  .dropdown_toggle > div > div > button {
    height: 50px !important;
  }
}

.schedule_modal_parent {
  max-width: 480px;
  background: #fff;
  border-radius: 4px;
  padding: 1rem 1rem !important;
  position: relative;
}
.schedule_modal_data h2 {
  font-family: Anton;
  font-size: 28px;
  font-weight: 400 !important;
  line-height: 38px;
  text-align: left;
  color: #000;
  margin: 0px !important;
  text-transform: uppercase;
  letter-spacing: 1px;
  padding-bottom: 12px;
}

.schedule_modal_data button {
  position: absolute;
  right: -9px;
  top: -9px;
  background: #f5f5f5;
  color: #000;
  border: unset !important;
  padding: 0px;
  font-size: 18px;
  outline: unset !important;
  box-shadow: unset !important;
  width: 40px;
  height: 40px;
  border-radius: 50% !important;
  display: flex;
  align-items: center;
  justify-content: center;
}
.schedule_modal_field select {
  border: unset;
  background: #f5f5f5;
  padding: 0px 15px 0px 15px;
  color: rgba(0, 0, 0, 0.6);
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 18px;
  width: 100%;
  font-family: Poppins;
  border-radius: 2px;
}
.schedule_modal_button {
  border-radius: 2px;

  font-size: 24px;
  font-style: normal;
  font-weight: 500;
  line-height: normal;

  width: 100%;
  cursor: pointer;
  height: 44px;
  text-transform: uppercase;
  padding: 0px;
}
@media (max-width: 540px) {
  .schedule_modal_data h2 {
    font-size: 24px;
    line-height: 30px;
  }
  .schedule_modal_button {
    font-size: 18px;
  }
}

.custom_2_parent {
  display: inline-block;
  width: 100%;
  margin-bottom: 60px;
  margin-top: 40px;
}
.custom_2_header {
  display: flex;
  justify-content: space-between;
  width: 100%;
  align-items: center;
  padding-bottom: 60px;
}
.custom_2_header small {
  flex-grow: 5;
  line-height: 0;
  text-align: center;
  position: relative;
  display: flex;
  align-items: center;
}

.custom_2_header small strong {
  width: 100%;
  height: 1px;
  background: #000;
}
.custom_2_header small span {
  min-width: 10px;
  min-height: 10px;
  background: #ffa800;
  border-radius: 50%;
  margin: 0px 8px;
}
.custom_2_header h3 {
  font-family: Anton;
  font-size: 90px;
  font-weight: 400;
  line-height: 94px;
  letter-spacing: 0em;
  text-align: center;
  padding: 0px 25px;
  margin: 0px;
  color: #000000;
  text-transform: uppercase;
}
.custom_2_body {
  padding: 0px 15px;
}
.custom_container {
  max-width: 1140px;
  margin: 0 auto;
}
.custom_2_data {
  display: flex;
  margin: 0px -10px;
  flex-wrap: wrap;
}
.custom_2_data .custom_2_single {
  flex: 0 0 33.333%;
  max-width: 100%;
  padding: 10px 10px;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.custom_2_single figure {
  width: 100%;
  display: flex;
  justify-content: center;
  max-width: 77px;
  margin: 0 auto;
  padding-bottom: 20px;
  max-height: 77px;
}
.custom_2_single span {
  font-family: Poppins;
  font-size: 24px;
  font-weight: 300;
  line-height: 36px;
  text-align: center;
}
.custom_2_single figure img {
  object-fit: contain;
}
@media (max-width: 1024px) {
  .custom_2_header {
    padding-bottom: 50px;
  }

  .custom_2_header h3 {
    font-size: 44px;
    line-height: 58px;
  }
  .custom_2_parent {
    margin-bottom: 40px;
  }
  .custom_2_single span {
    font-size: 16px;
    line-height: 21px;
  }
  .custom_2_single figure {
    max-width: 70px;
    max-height: 70px;
  }
}

@media (max-width: 768px) {
  .custom_2_header h3 {
    font-size: 38px;
    line-height: 52px;
  }
  .custom_2_header {
    padding-bottom: 35px;
  }
  .custom_2_parent {
    margin-bottom: 30px;
  }
  .custom_2_single figure {
    max-width: 50px;
    padding-bottom: 15px;
    max-height: 50px;
  }
}

@media (max-width: 540px) {
  .custom_2_header small span {
    min-width: 6px;
    min-height: 6px;
    margin: 0px 5px;
  }

  .custom_2_header h3 {
    padding: 0px 15px;
    font-size: 24px;
    line-height: 44px;
  }

  .custom_2_header {
    padding-bottom: 30px;
  }
  .custom_2_single span {
    font-size: 14px;

    line-height: 18px;
  }
  .custom_2_data {
    margin: 0px -6px;
  }
  .custom_2_data .custom_2_single {
    padding: 10px 6px;
  }
}
.custom_3_parent {
  display: inline-block;
  width: 100%;
}
.custom_3_header {
  display: flex;
  justify-content: space-between;
  width: 100%;
  align-items: center;
  padding-bottom: 60px;
}
.custom_3_header small {
  flex-grow: 5;
  line-height: 0;
  text-align: center;
  position: relative;
  display: flex;
  align-items: center;
}

.custom_3_header small strong {
  width: 100%;
  height: 1px;
  background: #000;
}
.custom_3_header small span {
  min-width: 10px;
  min-height: 10px;
  background: #ffa800;
  border-radius: 50%;
  margin: 0px 8px;
}
.custom_3_header h3 {
  font-family: Anton;
  font-size: 90px;
  font-weight: 400;
  line-height: 94px;
  letter-spacing: 0em;
  text-align: center;
  padding: 0px 25px;
  margin: 0px;
  color: #000000;
  text-transform: uppercase;
}

@media (max-width: 1024px) {
  .custom_3_header {
    padding-bottom: 50px;
  }

  .custom_3_header h3 {
    font-size: 44px;
    line-height: 58px;
  }
}

@media (max-width: 768px) {
  .custom_3_header h3 {
    font-size: 38px;
    line-height: 52px;
  }
  .custom_3_header {
    padding-bottom: 35px;
  }
}

@media (max-width: 540px) {
  .custom_3_header small span {
    min-width: 6px;
    min-height: 6px;
    margin: 0px 5px;
  }

  .custom_3_header h3 {
    padding: 0px 15px;
    font-size: 24px;
    line-height: 44px;
  }

  .custom_3_header {
    padding-bottom: 30px;
  }
}
.menu_btn_box {
  width: 100%;
  text-align: center;
  display: flex;
  justify-content: center;
  padding-top: 15px;
  margin-top: 45px;
  margin-bottom: 60px;
}
.menu_btn_box a {
  font-family: Poppins;
  font-size: 23px;
  font-weight: 400;
  line-height: 23px;
  letter-spacing: 0em;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 208px;
  height: 52px;
  border-radius: 2px;
  text-transform: capitalize;
  background: #000;
  color: #fff !important;
  position: relative;
}
@media (max-width: 1024px) {
  .menu_btn_box a {
    font-size: 16px;
    width: 155px;
    height: 42px;
  }
  .menu_btn_box {
    margin-bottom: 40px;
  }
}
@media (max-width: 768px) {
  .menu_btn_box a {
    height: 43px;
    width: 148px;
  }
  .menu_btn_box {
    margin-bottom: 30px;
  }
}
@media (max-width: 540px) {
  .menu_btn_box a {
    width: 132px;
    height: 40px;
    font-size: 14px;
  }
}

.testimonial_section {
  display: block;
  width: 100%;
  padding: 62px 15px 62px 15px;
  float: left;
  background: linear-gradient(180deg, #ededed 0%, rgba(217, 217, 217, 0) 48.5%);
}
.custom_container {
  max-width: 1140px;
  margin: 0px auto;
}
.testimonial_heading {
  display: flex;
  justify-content: space-between;
  width: 100%;
  align-items: center;
  padding-bottom: 77px;
}
.testimonial_heading small {
  flex-grow: 5;
  line-height: 0;
  text-align: center;
  position: relative;
  display: flex;
  align-items: center;
}
.testimonial_heading small strong {
  width: 100%;
  height: 1px;
  background: #000;
}
.testimonial_heading small span {
  min-width: 10px;
  min-height: 10px;
  background: #ffa800;
  border-radius: 50%;
  margin: 0px 8px;
}

.testimonial_heading h3 {
  font-family: Anton;
  text-align: center;
  padding: 0px 25px;
  margin: 0px;
  font-size: 70px;
  font-weight: 400;
  line-height: 96px;
  letter-spacing: 0em;
  color: #000;
  margin-bottom: 0px;
  text-transform: uppercase;
  position: relative;
}
.testimonial_description img.right-side {
  float: right;
}
.testimonial_description img.left-side {
  float: left;
}
.testimonial_main {
  float: left;
  width: 100%;
}

.testimonial_list {
  float: left;
  width: 100%;
  position: relative;
  padding: 0px 10px;
}
.testimonial_list ul {
  margin: 0px -15px;
  padding: 0px;
}
.testimonial_list ul li {
  list-style: none;
  width: 25%;
  padding: 0px 15px 0px 15px;
  float: left;
  margin: 0px !important;
}
.testimonial_data_box {
  margin: 16px 0px 16px;
  width: 100%;
  display: flex;
  background: #ffffff;
  border-radius: 12px;
  flex-direction: column;
  box-shadow: 0px 4px 13px 0px #00000040;
}
.testimonial_figure_box {
  width: 100%;
  display: flex;
  align-items: center;
  position: relative;
}
.testimonial_figure_box figure {
  width: 100%;
  height: 48px;
  position: absolute;
  margin: auto;
  text-align: center;
  display: flex;
  justify-content: center;
  top: -2px;
}

.testimonial_figure_box figure img {
  object-fit: cover;
  border-radius: 50%;
  width: 48px;
  height: 48px;
}
.testimonial_reviewer_data {
  display: flex;
  flex-direction: column;
}
.testimonial_reviewer_data h4 {
  font-family: Poppins;
  font-size: 15px;
  font-weight: 600;
  line-height: 23px;
  letter-spacing: 0em;
  color: #212121;
  margin: 0px;
  text-transform: capitalize;
}
.testimonial_reviewer_data span {
  font-family: Poppins;
  font-size: 14px;
  font-weight: 400;
  line-height: 21px;
  letter-spacing: 0em;
  color: #9fa19d;
}
.testimonial_google_img img {
  width: 22px;
}
.testimonial_rating_stars {
  display: flex;
  align-items: center;
  padding-bottom: 11px;
  margin: auto;
  text-align: center;
  justify-content: center;
}
.testimonial_rating_stars i {
  color: #ffc10785;
  padding-right: 4px;
  font-size: 10px;
  margin-top: 10px;
}
.testimonial_rating_stars i:last-child {
  padding-right: 0px;
}
.testimonial_description {
  padding-bottom: 16px;
  display: flex;
  flex-direction: column;
  width: 100%;
}
.testimonial_description p {
  color: rgba(96, 125, 139, 1);
  width: 100%;
  font-family: Lato;
  font-size: 12px;
  font-weight: 400;
  line-height: 15px;
  letter-spacing: 0.015em;
  text-align: center;
}
.testimonial_review_data_main .testimonial_description img {
  width: 21px;
  height: 19.52px;
}
.testimonial_slider ul.slick-dots {
  display: flex;
  width: 100%;
  justify-content: center;
  align-items: center;
  margin: 0px !important;
  padding-top: 40px;
}
.testimonial_slider ul.slick-dots li {
  width: unset !important;
  height: unset !important;
  padding: 0px 5px;
}

.testimonial_slider .slick-dots li button {
  width: 10px !important;
  height: 10px !important;
  padding: 5px;
  cursor: pointer;
  color: white;
  border: 0;
  outline: none;
  background: transparent !important;
  border-radius: 50% !important;
  border: 1px solid #000 !important;
  position: relative;
  color: transparent;
}
.testimonial_slider li.slick-active button {
  background: #000 !important;
  border: 1px solid #000 !important;
}
.testimonial_slider .slick-dots li button:before {
  display: none !important;
}
.testimonial_review_data_main {
  display: flex;
  align-items: center;
  flex-direction: column;
  padding: 34px 20px 27px 20px;
  border-radius: 14px;
  position: relative;
  box-shadow: 0px 15px 15px 0px #00000040;
}
.testimonial_review_data_main-second {
  padding: 34px 20px 27px 20px;
  border-radius: 14px;
  text-align: center;
}
@media (max-width: 1024px) {
  .testimonial_section {
    padding: 40px 15px 40px 15px;
  }

  .testimonial_list ul {
    margin: 0px -10px;
  }
  .testimonial_list ul li {
    padding: 0px 10px 0px 10px;
  }

  .testimonial_heading h3 {
    font-size: 44px;
    line-height: 58px;
  }
  .testimonial_heading {
    padding-bottom: 30px;
  }
}
@media (max-width: 768px) {
  .testimonial_section {
    padding: 30px 15px 30px 15px;
  }
  .testimonial_heading h3 {
    font-size: 25px;
    line-height: 43px;
  }
  .testimonial_slider ul.slick-dots {
    padding-top: 20px;
  }
  .testimonial_heading {
    justify-content: center;
    padding-bottom: 20px;
  }
  .testimonial_heading small {
    display: none;
  }

  .testimonial_slider .slick-slide {
    padding: 0px 10px;
  }

  .testimonial_list {
    padding: 0px 5px;
  }
}
@media (max-width: 540px) {
  .testimonial_heading h3 {
    font-size: 24px;
    line-height: 44px;
  }

  .testimonial_slider .slick-slide {
    padding: 0px 8px;
  }

  .testimonial_list {
    padding: 0px 3px;
  }

  .testimonial_data_box {
    margin: 8px 0px;
  }
}

.signup_btn {
  width: 100%;
  text-align: center;
  display: flex;
  justify-content: center;
  padding-top: 20px;
}
.signup_btn a {
  font-family: Poppins;
  font-size: 23px;
  font-weight: 400;
  line-height: 23px;
  letter-spacing: 0em;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 208px;
  height: 52px;
  border-radius: 2px;
  text-transform: capitalize;
  background: #000;
  color: #fff !important;
  position: relative;
}
.testimonial_slider li {
  list-style: none;
  padding: 0px 15px 0px 15px;
  float: left;
  margin: 0px !important;
}

.testimonial_slider .slick-slide {
  padding: 0px 15px;
}

.testimonial_slider .slick-track {
  display: flex;
  align-items: stretch;
}

.testimonial_slider .slick-slide > div {
  height: 100%;
}

.testimonial_slider .slick-slide .testimonial_data_box {
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* Fix for touch/swipe functionality */
.testimonial_slider .slick-list {
  overflow: hidden;
  touch-action: pan-y;
}

.testimonial_slider .slick-track {
  touch-action: pan-y;
}

/* Ensure slider is responsive */
.testimonial_slider {
  width: 100%;
  max-width: 100%;
}

/* Hide default arrows on mobile for better touch experience */
@media (max-width: 768px) {
  .testimonial_slider .slick-prev,
  .testimonial_slider .slick-next {
    display: none !important;
  }
}

@media (max-width: 1024px) {
  .signup_btn a {
    font-size: 18px;
    width: 155px;
    height: 42px;
  }
}
@media (max-width: 768px) {
  .signup_btn a {
    height: 43px;
    width: 148px;
  }
}
@media (max-width: 540px) {
  .signup_btn a {
    width: 132px;
    height: 40px;
    font-size: 16px;
  }
}
.testimonial_ab_fig {
  width: 100%;
  float: left;
  transform: translatey(10px);
}

.testimonial_ab_fig img {
  width: 100%;
  object-fit: cover;
}

.custom-container {
  max-width: 1140px;
  margin: 0 auto;
}

.footer-section {
  background: #000000;
  color: #fff;
  float: left;
  width: 100%;
  padding: 56px 15px 54px 15px;
}

.footer-data-box {
  display: grid;
  width: 100%;
  grid-template-columns: 1.4fr 1fr 1fr 1fr 1fr;
  grid-template-rows: auto;
  grid-template-areas: ". . . .";
  gap: 30px 30px;
}

.footer-detail-box-one {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.footer-detail-box-one figure {
  margin: 0px !important;
  width: 131px !important;
  padding-top: 6px;
}

.footer-detail-box-one figure img {
  width: 100%;
}

.footer-detail-box-two {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.footer-detail-box-heading {
  position: relative;
  padding-bottom: 14px;
}

.footer-detail-box-heading h3 {
  margin: 0px !important;
  font-family: Anton;
  font-size: 18px;
  font-weight: 400;
  line-height: 32px;
  letter-spacing: 0em;
  color: #ffffff;
  text-transform: uppercase;
}

.footer-detail-box-content-list {
  display: flex;
  flex-direction: column;
  width: 100%;
  padding: 0px !important;
  margin: 0px !important;
}

.footer-detail-box-content-list li {
  list-style: none;
  display: flex;
  align-items: center;
  padding-bottom: 14px;
}

.footer-detail-box-content-list li:last-child {
  padding-bottom: 0px;
}

.footer-detail-box-content-list li a {
  font-family: Poppins;
  font-size: 13px;
  font-weight: 500;
  line-height: 20px;
  letter-spacing: 0em;
  text-decoration: none;
  color: #fff;
  text-transform: uppercase;
}

.footer-detail-box-content-list li a:hover {
  color: #fff;
}

.footer-contact-list {
  display: flex;
  flex-direction: column;
  width: 100%;
  padding: 0px !important;
  margin: 0px !important;
}

.footer-contact-list li {
  list-style: none;
  display: flex;
  padding-bottom: 14px;
}

.footer-contact-list li:last-child {
  padding-bottom: 0px;
}

.footer-contact-list li i {
  padding-right: 6px;
  font-size: 16px;
  line-height: 18px;
  color: #231f20;
}

.footer-contact-list li i:before {
  color: #fff !important;
}

.footer-contact-list li a {
  font-family: Poppins;
  font-size: 13px;
  font-weight: 500;
  line-height: 20px;
  letter-spacing: 0em;
  color: #fff;
  text-decoration: none;
}

.footer-contact-list li a:hover {
  color: #fff;
}

.footer-socail-links {
  display: flex;
  align-items: center;
  padding-top: 15px;
}

.footer-socail-links a {
  background: rgba(255, 255, 255, 0.2);
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  border-radius: 50%;
  justify-content: center;
  margin-right: 10px;
}

.footer-socail-links a i {
  color: #ffffff;
  font-size: 16px;
}

@media (max-width: 1024px) {
  .footer-data-box {
    grid-template-columns: 1.4fr 1.5fr 1.2fr;
    grid-template-areas: ". . .";
  }
}

@media (max-width: 768px) {
  .footer-data-box {
    grid-template-columns: 1.6fr 1fr;
    grid-template-areas: ". .";
  }
}

@media (max-width: 540px) {
  .footer-section {
    padding: 40px 15px 40px 15px;
  }

  .footer-data-box {
    grid-template-columns: 1fr;
    grid-template-areas: ".";
    gap: 30px 0px;
  }

  .footer-detail-box-one {
    justify-content: center;
    align-items: center;
  }

  .footer-detail-box-one p {
    padding-right: 0px;
    text-align: center;
  }

  .footer-detail-box-heading {
    padding-bottom: 0px;
  }

  .footer-contact-list {
    padding-top: 15px !important;
  }

  .footer-detail-box-content-list {
    padding-top: 15px !important;
  }

  .footer-socail-links {
    justify-content: center;
    align-items: center;
  }

  .panel {
    display: none;
    overflow: hidden;
    transition: 0.5s ease-out;
    text-align: left;
  }

  .footer-detail-box-heading.accordion:after {
    content: "\002B";
    color: #fff;
    font-family: "revicons";
    font-size: 26px;
    position: absolute;
    height: 100%;
    display: flex;
    align-items: center;
    margin: 0px;
    top: 0px;
    right: 0px;
  }

  .footer-detail-box-heading.active:after {
    content: "\2212" !important;
    color: #fff;
    font-family: "revicons";
    font-size: 21px;
    position: absolute;
    height: 100%;
    display: flex;
    align-items: center;
    margin: 0px;
    top: 0px;
    right: 0px;
  }
}

@media (min-width: 540px) {
  .modcontent.panel {
    display: block !important;
  }
}

.custom-bullet {
  position: relative;
  padding-left: 1.25rem; /* equivalent to pl-5 */
}

.custom-bullet::before {
  content: "•";
  position: absolute;
  left: 0;
  top: -4px;
  font-size: 2rem; /* Increased bullet size */
  line-height: 1;
}

.bullet-red::before {
  color: #ef4444; /* Tailwind red-500 */
}

.bullet-orange::before {
  color: #f97316; /* Tailwind orange-500 */
}

.bullet-purple::before {
  color: #8b5cf6; /* Tailwind purple-500 */
}

.bullet-red-dark::before {
  color: #991b1b; /* Tailwind red-800 */
}

.bullet-yellow::before {
  color: #eab308; /* Tailwind yellow-500 */
}

.bullet-brown::before {
  color: #78350f; /* Tailwind equivalent for brown-ish (orange-900) */
}
.grid-cols-3-custom {
  display: grid;
  grid-template-columns: repeat(2, minmax(0, 1fr));
  gap: 1rem;
}

@media (min-width: 768px) {
  .grid-cols-3-custom {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }
}

/* phone no field flag width on checkout */
.react-international-phone-country-selector {
  width: max-content;
}
